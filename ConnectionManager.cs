using System;
using System.Collections.Generic;
using TaleWorlds.Library;

namespace LAN_Fight
{
    /// <summary>
    /// 连接状态枚举
    /// </summary>
    public enum ConnectionState
    {
        Disconnected,   // 未连接
        Connecting,     // 连接中
        Connected,      // 已连接
        Hosting,        // 主机模式
        Error           // 错误状态
    }

    /// <summary>
    /// 连接管理器
    /// 负责管理网络连接状态和提供统一的连接接口
    /// </summary>
    public class ConnectionManager
    {
        private NetworkManager _networkManager;
        private RoomManager _roomManager;
        private ConnectionState _currentState;
        private string _lastError;
        private DateTime _lastStateChange;

        // 事件
        public event Action<ConnectionState, ConnectionState> OnStateChanged;
        public event Action<string> OnStatusMessage;
        public event Action<string> OnError;

        public ConnectionState CurrentState => _currentState;
        public string LastError => _lastError;
        public bool IsConnected => _currentState == ConnectionState.Connected || _currentState == ConnectionState.Hosting;
        public bool IsHost => _currentState == ConnectionState.Hosting;

        public NetworkManager NetworkManager => _networkManager;
        public RoomManager RoomManager => _roomManager;

        public ConnectionManager()
        {
            _networkManager = new NetworkManager();
            _roomManager = new RoomManager(_networkManager);
            _currentState = ConnectionState.Disconnected;
            _lastStateChange = DateTime.Now;

            // 订阅网络事件
            _networkManager.OnConnectionStatusChanged += HandleConnectionStatusChanged;
            _networkManager.OnPlayerJoined += HandlePlayerJoined;
            _networkManager.OnPlayerLeft += HandlePlayerLeft;

            // 订阅房间事件
            _roomManager.OnRoomCreated += HandleRoomCreated;
            _roomManager.OnRoomError += HandleRoomError;
            _roomManager.OnRoomStateChanged += HandleRoomStateChanged;
        }

        /// <summary>
        /// 创建房间并开始主机
        /// </summary>
        public bool StartHost(string roomName = "LAN Fight Room", string password = "", int maxPlayers = 4)
        {
            try
            {
                if (IsConnected)
                {
                    OnError?.Invoke("已在连接状态，无法创建新房间");
                    return false;
                }

                ChangeState(ConnectionState.Connecting);
                OnStatusMessage?.Invoke("正在创建房间...");

                bool success = _roomManager.CreateRoom(roomName, password, maxPlayers);
                
                if (success)
                {
                    ChangeState(ConnectionState.Hosting);
                    OnStatusMessage?.Invoke($"房间创建成功: {roomName}");
                    return true;
                }
                else
                {
                    ChangeState(ConnectionState.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                HandleError($"创建房间失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 连接到房间
        /// </summary>
        public bool JoinRoom(string serverAddress, string password = "", string playerName = "Player")
        {
            try
            {
                if (IsConnected)
                {
                    OnError?.Invoke("已在连接状态，无法加入新房间");
                    return false;
                }

                ChangeState(ConnectionState.Connecting);
                OnStatusMessage?.Invoke($"正在连接到 {serverAddress}...");

                bool success = _roomManager.JoinRoom(serverAddress, password, playerName);
                
                if (success)
                {
                    // 连接状态将在网络事件中更新
                    return true;
                }
                else
                {
                    ChangeState(ConnectionState.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                HandleError($"加入房间失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public void Disconnect()
        {
            try
            {
                if (_currentState == ConnectionState.Disconnected) return;

                OnStatusMessage?.Invoke("正在断开连接...");
                
                _roomManager.LeaveRoom();
                ChangeState(ConnectionState.Disconnected);
                
                OnStatusMessage?.Invoke("已断开连接");
            }
            catch (Exception ex)
            {
                HandleError($"断开连接时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新连接管理器
        /// </summary>
        public void Update(TimeSpan deltaTime)
        {
            try
            {
                _networkManager?.Update(deltaTime);
                
                // 检查连接超时
                CheckConnectionTimeout();
            }
            catch (Exception ex)
            {
                HandleError($"更新连接管理器时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取连接信息
        /// </summary>
        public string GetConnectionInfo()
        {
            var info = new List<string>
            {
                $"状态: {GetStateDisplayName(_currentState)}",
                $"上次状态改变: {_lastStateChange:HH:mm:ss}"
            };

            if (_roomManager.IsInRoom)
            {
                var room = _roomManager.CurrentRoom;
                info.Add($"房间: {room.RoomName}");
                info.Add($"玩家: {room.CurrentPlayerCount}/{room.MaxPlayers}");
                info.Add($"房间状态: {room.State}");
            }

            if (_networkManager.IsRunning)
            {
                info.Add($"网络连接数: {_networkManager.ConnectedPlayersCount}");
            }

            if (!string.IsNullOrEmpty(_lastError))
            {
                info.Add($"最后错误: {_lastError}");
            }

            return string.Join("\n", info);
        }

        /// <summary>
        /// 改变连接状态
        /// </summary>
        private void ChangeState(ConnectionState newState)
        {
            if (_currentState == newState) return;

            var oldState = _currentState;
            _currentState = newState;
            _lastStateChange = DateTime.Now;

            // 清除错误信息（除非是错误状态）
            if (newState != ConnectionState.Error)
            {
                _lastError = null;
            }

            OnStateChanged?.Invoke(oldState, newState);
            Debug.Print($"[LAN Fight] 连接状态改变: {oldState} -> {newState}");
        }

        /// <summary>
        /// 处理错误
        /// </summary>
        private void HandleError(string error)
        {
            _lastError = error;
            ChangeState(ConnectionState.Error);
            OnError?.Invoke(error);
            Debug.Print($"[LAN Fight] 连接错误: {error}");
        }

        /// <summary>
        /// 检查连接超时
        /// </summary>
        private void CheckConnectionTimeout()
        {
            if (_currentState == ConnectionState.Connecting)
            {
                var elapsed = DateTime.Now - _lastStateChange;
                if (elapsed.TotalMilliseconds > NetworkConfiguration.CONNECTION_TIMEOUT)
                {
                    HandleError("连接超时");
                }
            }
        }

        /// <summary>
        /// 获取状态显示名称
        /// </summary>
        private string GetStateDisplayName(ConnectionState state)
        {
            return state switch
            {
                ConnectionState.Disconnected => "未连接",
                ConnectionState.Connecting => "连接中",
                ConnectionState.Connected => "已连接",
                ConnectionState.Hosting => "主机模式",
                ConnectionState.Error => "错误",
                _ => state.ToString()
            };
        }

        #region 事件处理

        private void HandleConnectionStatusChanged(string status)
        {
            OnStatusMessage?.Invoke(status);
            
            // 根据状态消息更新连接状态
            if (status.Contains("已连接") && _currentState == ConnectionState.Connecting)
            {
                ChangeState(ConnectionState.Connected);
            }
            else if (status.Contains("断开") && IsConnected)
            {
                ChangeState(ConnectionState.Disconnected);
            }
        }

        private void HandlePlayerJoined(LiteNetLib.NetPeer peer, string playerInfo)
        {
            OnStatusMessage?.Invoke($"玩家加入: {peer.EndPoint}");
        }

        private void HandlePlayerLeft(LiteNetLib.NetPeer peer, string reason)
        {
            OnStatusMessage?.Invoke($"玩家离开: {peer.EndPoint} ({reason})");
        }

        private void HandleRoomCreated(RoomInfo room)
        {
            OnStatusMessage?.Invoke($"房间创建成功: {room.RoomName}");
        }

        private void HandleRoomError(string error)
        {
            HandleError(error);
        }

        private void HandleRoomStateChanged(RoomState newState)
        {
            OnStatusMessage?.Invoke($"房间状态: {newState}");
        }

        #endregion

        public void Dispose()
        {
            Disconnect();
            _networkManager?.Dispose();
        }
    }
}
