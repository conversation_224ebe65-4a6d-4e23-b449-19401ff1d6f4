# LAN Fight Mod - 骑砍2局域网联机模组

## 简介

LAN Fight Mod 是一个为《骑马与砍杀2：霸主》开发的局域网多人联机模组。该模组允许玩家在沙盒模式中进行局域网联机游戏，支持玩家1创建房间，玩家2加入房间的基础联机功能。

## 功能特性

- ✅ 局域网房间创建和加入
- ✅ 基础网络通信（基于LiteNetLib）
- ✅ 简单的用户界面
- ✅ 游戏内快捷键支持
- ✅ 连接状态管理
- ✅ 调试和诊断工具
- 🚧 战斗中的多人控制（计划中）
- 🚧 副将NPC管理（计划中）

## 系统要求

- 骑马与砍杀2：霸主 v1.5.9 或更高版本
- .NET Framework 4.7.2
- Windows 操作系统
- 局域网环境

## 安装说明

### 1. 编译模组

1. 确保您的开发环境已安装：
   - Visual Studio 2019 或更高版本
   - .NET Framework 4.7.2 SDK

2. 打开 `LAN_Fight.sln` 解决方案文件

3. 检查项目引用路径：
   - 确保所有骑砍2的DLL引用路径正确指向您的游戏安装目录
   - 默认路径：`D:\Steam\steamapps\common\Mount & Blade II Bannerlord`

4. 编译项目：
   - 选择 `Release` 配置
   - 右键点击项目 -> 生成

### 2. 安装到游戏

1. 在骑砍2模组目录创建新文件夹：
   ```
   [游戏目录]\Modules\LAN_Fight\
   ```

2. 复制以下文件到模组目录：
   ```
   LAN_Fight\
   ├── SubModule.xml
   ├── bin\
   │   └── Win64_Shipping_Client\
   │       ├── LAN_Fight.dll
   │       └── LiteNetLib.dll
   ```

3. 启动游戏启动器，在模组列表中启用 "LAN Fight" 模组

## 使用方法

### 快捷键

- **F9** - 创建房间
- **F10** - 加入房间  
- **F11** - 查看连接状态
- **F12** - 显示帮助信息

### 创建房间（玩家1）

1. 启动游戏并进入主菜单
2. 点击 "创建局域网房间" 或按 F9
3. 输入房间名称
4. 等待其他玩家加入

### 加入房间（玩家2）

1. 启动游戏并进入主菜单
2. 点击 "加入局域网房间" 或按 F10
3. 输入主机的IP地址（如：*************）
4. 等待连接成功

### 本地测试

如果要在同一台电脑上测试：
1. 启动两个游戏实例
2. 第一个实例创建房间
3. 第二个实例使用 `127.0.0.1` 或 `localhost` 加入

## 网络配置

### 默认设置

- **端口**: 7777
- **最大玩家数**: 4
- **连接超时**: 30秒
- **心跳间隔**: 5秒

### 防火墙设置

确保防火墙允许以下连接：
- 入站规则：TCP/UDP 端口 7777
- 出站规则：TCP/UDP 端口 7777

### 路由器设置（如需要）

如果要通过互联网连接，需要在路由器中设置端口转发：
- 外部端口：7777
- 内部端口：7777
- 协议：TCP/UDP
- 内部IP：主机电脑的局域网IP

## 故障排除

### 常见问题

1. **无法创建房间**
   - 检查端口7777是否被占用
   - 确认防火墙设置
   - 查看调试信息（F11）

2. **无法加入房间**
   - 确认IP地址正确
   - 检查网络连接
   - 确认主机已创建房间

3. **连接中断**
   - 检查网络稳定性
   - 确认防火墙未阻止连接
   - 重启模组

### 调试工具

模组内置了调试工具：
- 按 F11 查看连接状态
- 查看游戏日志文件
- 使用诊断报告功能

## 开发信息

### 项目结构

```
LAN_Fight/
├── LANFightSubModule.cs      # 主模块类
├── NetworkManager.cs         # 网络管理器
├── ConnectionManager.cs      # 连接管理器
├── RoomManager.cs           # 房间管理器
├── NetworkMessages.cs       # 网络消息定义
├── NetworkConfiguration.cs  # 网络配置
├── LANFightUI.cs           # 用户界面
├── LANFightHotkeys.cs      # 快捷键管理
├── LANFightDebugger.cs     # 调试工具
└── SubModule.xml           # 模组配置文件
```

### 依赖项

- **LiteNetLib**: 轻量级网络库
- **Harmony**: 运行时代码修改
- **TaleWorlds.*** : 骑砍2游戏API

### 扩展开发

如需扩展功能，可以：
1. 继承现有的管理器类
2. 添加新的网络消息类型
3. 实现自定义的UI界面
4. 添加游戏逻辑同步

## 版本历史

### v1.0.0 (当前版本)
- 基础局域网连接功能
- 房间创建和加入
- 简单用户界面
- 快捷键支持
- 调试工具

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交问题报告和功能请求。如需贡献代码，请：
1. Fork 本项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- 游戏社区论坛

## 致谢

- TaleWorlds Entertainment - 骑马与砍杀2
- LiteNetLib 开发团队
- BannerlordCoop 社区项目（参考）
- 骑砍2模组开发社区

---

**注意**: 本模组仍在开发中，可能存在bug和不稳定的情况。请在使用前备份您的存档文件。
