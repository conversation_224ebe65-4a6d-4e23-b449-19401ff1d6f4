using System;
using System.Collections.Generic;
using LiteNetLib;
using LiteNetLib.Utils;
using TaleWorlds.Library;

namespace LAN_Fight
{
    /// <summary>
    /// 网络管理器
    /// 负责处理局域网连接、消息传递和玩家管理
    /// </summary>
    public class NetworkManager : INetEventListener, IDisposable
    {
        private NetManager _netManager;
        private NetworkConfiguration _config;
        private bool _isHost;
        private bool _isRunning;
        private NetPeer _serverPeer; // 客户端连接到的服务器
        private List<NetPeer> _connectedPeers; // 服务器端连接的客户端列表

        // 事件
        public event Action<string> OnConnectionStatusChanged;
        public event Action<NetPeer, string> OnPlayerJoined;
        public event Action<NetPeer, string> OnPlayerLeft;
        public event Action<NetPeer, string> OnMessageReceived;

        public bool IsHost => _isHost;
        public bool IsRunning => _isRunning;
        public int ConnectedPlayersCount => _connectedPeers?.Count ?? 0;

        public NetworkManager()
        {
            _config = new NetworkConfiguration();
            _connectedPeers = new List<NetPeer>();
            InitializeNetManager();
        }

        /// <summary>
        /// 初始化网络管理器
        /// </summary>
        private void InitializeNetManager()
        {
            _netManager = new NetManager(this)
            {
                DisconnectTimeout = _config.CONNECTION_TIMEOUT,
                PingInterval = _config.PING_INTERVAL,
                ReconnectDelay = _config.RECONNECT_DELAY,
                UnconnectedMessagesEnabled = true
            };
        }

        /// <summary>
        /// 作为主机启动
        /// </summary>
        public void StartAsHost(int port = NetworkConfiguration.DEFAULT_PORT)
        {
            if (_isRunning)
            {
                Debug.Print("[LAN Fight] 网络管理器已在运行中");
                return;
            }

            try
            {
                _isHost = true;
                _config.IsHost = true;
                _config.ServerPort = port;

                if (_netManager.Start(port))
                {
                    _isRunning = true;
                    OnConnectionStatusChanged?.Invoke($"房间已创建，端口: {port}");
                    Debug.Print($"[LAN Fight] 服务器启动成功，端口: {port}");
                }
                else
                {
                    throw new Exception($"无法在端口 {port} 启动服务器");
                }
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 启动服务器失败: {ex.Message}");
                OnConnectionStatusChanged?.Invoke($"创建房间失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 作为客户端连接
        /// </summary>
        public void StartAsClient(string serverAddress, int port = NetworkConfiguration.DEFAULT_PORT)
        {
            if (_isRunning)
            {
                Debug.Print("[LAN Fight] 网络管理器已在运行中");
                return;
            }

            try
            {
                _isHost = false;
                _config.IsHost = false;
                _config.ServerAddress = serverAddress;
                _config.ServerPort = port;

                _netManager.Start();
                _serverPeer = _netManager.Connect(serverAddress, port, NetworkConfiguration.CONNECTION_KEY);
                
                if (_serverPeer != null)
                {
                    _isRunning = true;
                    OnConnectionStatusChanged?.Invoke($"正在连接到 {serverAddress}:{port}...");
                    Debug.Print($"[LAN Fight] 开始连接到服务器: {serverAddress}:{port}");
                }
                else
                {
                    throw new Exception("无法创建连接");
                }
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 连接服务器失败: {ex.Message}");
                OnConnectionStatusChanged?.Invoke($"连接失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public void Disconnect()
        {
            if (!_isRunning) return;

            try
            {
                _isRunning = false;
                
                if (_isHost)
                {
                    // 断开所有客户端连接
                    _netManager.DisconnectAll();
                    _connectedPeers.Clear();
                }
                else
                {
                    // 断开与服务器的连接
                    _serverPeer?.Disconnect();
                    _serverPeer = null;
                }

                _netManager.Stop();
                OnConnectionStatusChanged?.Invoke("已断开连接");
                Debug.Print("[LAN Fight] 网络连接已断开");
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 断开连接时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新网络状态
        /// </summary>
        public void Update(TimeSpan deltaTime)
        {
            if (_isRunning && _netManager != null)
            {
                _netManager.PollEvents();
            }
        }

        /// <summary>
        /// 发送消息给所有连接的玩家
        /// </summary>
        public void SendMessageToAll(string message)
        {
            if (!_isRunning) return;

            NetDataWriter writer = new NetDataWriter();
            writer.Put(message);

            if (_isHost)
            {
                // 服务器发送给所有客户端
                _netManager.SendToAll(writer, DeliveryMethod.ReliableOrdered);
            }
            else if (_serverPeer != null)
            {
                // 客户端发送给服务器
                _serverPeer.Send(writer, DeliveryMethod.ReliableOrdered);
            }
        }

        /// <summary>
        /// 发送消息给特定玩家
        /// </summary>
        public void SendMessageToPeer(NetPeer peer, string message)
        {
            if (!_isRunning || peer == null) return;

            NetDataWriter writer = new NetDataWriter();
            writer.Put(message);
            peer.Send(writer, DeliveryMethod.ReliableOrdered);
        }

        #region INetEventListener 实现

        public void OnPeerConnected(NetPeer peer)
        {
            Debug.Print($"[LAN Fight] 玩家连接: {peer.EndPoint}");
            
            if (_isHost)
            {
                _connectedPeers.Add(peer);
                OnPlayerJoined?.Invoke(peer, peer.EndPoint.ToString());
                OnConnectionStatusChanged?.Invoke($"玩家加入房间: {peer.EndPoint} (总计: {_connectedPeers.Count})");
            }
            else
            {
                OnConnectionStatusChanged?.Invoke($"已连接到房间: {peer.EndPoint}");
            }
        }

        public void OnPeerDisconnected(NetPeer peer, DisconnectInfo disconnectInfo)
        {
            Debug.Print($"[LAN Fight] 玩家断开连接: {peer.EndPoint}, 原因: {disconnectInfo.Reason}");
            
            if (_isHost)
            {
                _connectedPeers.Remove(peer);
                OnPlayerLeft?.Invoke(peer, disconnectInfo.Reason.ToString());
                OnConnectionStatusChanged?.Invoke($"玩家离开房间: {peer.EndPoint} (总计: {_connectedPeers.Count})");
            }
            else
            {
                _serverPeer = null;
                OnConnectionStatusChanged?.Invoke($"与房间断开连接: {disconnectInfo.Reason}");
            }
        }

        public void OnNetworkReceive(NetPeer peer, NetPacketReader reader, DeliveryMethod deliveryMethod)
        {
            try
            {
                string message = reader.GetString();
                Debug.Print($"[LAN Fight] 收到消息来自 {peer.EndPoint}: {message}");
                OnMessageReceived?.Invoke(peer, message);
                
                // 如果是服务器，转发消息给其他客户端
                if (_isHost)
                {
                    foreach (var otherPeer in _connectedPeers)
                    {
                        if (otherPeer != peer)
                        {
                            SendMessageToPeer(otherPeer, $"[{peer.EndPoint}]: {message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 处理接收消息时出错: {ex.Message}");
            }
            finally
            {
                reader.Recycle();
            }
        }

        public void OnNetworkError(System.Net.IPEndPoint endPoint, System.Net.Sockets.SocketError socketError)
        {
            Debug.Print($"[LAN Fight] 网络错误: {endPoint}, 错误: {socketError}");
            OnConnectionStatusChanged?.Invoke($"网络错误: {socketError}");
        }

        public void OnNetworkReceiveUnconnected(System.Net.IPEndPoint remoteEndPoint, NetPacketReader reader, UnconnectedMessageType messageType)
        {
            // 处理未连接的消息（如房间发现等）
            reader.Recycle();
        }

        public void OnNetworkLatencyUpdate(NetPeer peer, int latency)
        {
            // 延迟更新
        }

        public void OnConnectionRequest(ConnectionRequest request)
        {
            if (_isHost)
            {
                if (_connectedPeers.Count < NetworkConfiguration.MAX_CONNECTIONS)
                {
                    if (request.Data.GetString() == NetworkConfiguration.CONNECTION_KEY)
                    {
                        request.Accept();
                        Debug.Print($"[LAN Fight] 接受连接请求: {request.RemoteEndPoint}");
                    }
                    else
                    {
                        request.Reject();
                        Debug.Print($"[LAN Fight] 拒绝连接请求（密钥错误）: {request.RemoteEndPoint}");
                    }
                }
                else
                {
                    request.Reject();
                    Debug.Print($"[LAN Fight] 拒绝连接请求（房间已满）: {request.RemoteEndPoint}");
                }
            }
        }

        #endregion

        public void Dispose()
        {
            Disconnect();
            _netManager?.Stop();
            _connectedPeers?.Clear();
        }
    }
}
