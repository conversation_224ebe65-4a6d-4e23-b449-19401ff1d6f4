@echo off
echo ========================================
echo LAN Fight Mod 自动编译和部署脚本
echo ========================================
echo.

REM 设置变量
set PROJECT_NAME=LAN_Fight
set GAME_PATH=D:\Steam\steamapps\common\Mount ^& Blade II Bannerlord
set MOD_PATH=%GAME_PATH%\Modules\%PROJECT_NAME%
set MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe

REM 检查MSBuild路径
if not exist "%MSBUILD_PATH%" (
    echo 错误: 找不到MSBuild.exe
    echo 请检查Visual Studio安装路径
    echo 当前查找路径: %MSBUILD_PATH%
    pause
    exit /b 1
)

REM 检查游戏路径
if not exist "%GAME_PATH%" (
    echo 错误: 找不到游戏目录
    echo 请检查游戏安装路径: %GAME_PATH%
    pause
    exit /b 1
)

echo 1. 清理旧的编译文件...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo 2. 编译项目...
"%MSBUILD_PATH%" "%PROJECT_NAME%.csproj" /p:Configuration=Release /p:Platform=x64 /p:LangVersion=8.0 /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 3. 检查编译输出...
if not exist "bin\Win64_Shipping_Client\%PROJECT_NAME%.dll" (
    echo 错误: 找不到编译输出文件
    echo 预期位置: bin\Win64_Shipping_Client\%PROJECT_NAME%.dll
    pause
    exit /b 1
)

echo 4. 创建Mod目录...
if not exist "%MOD_PATH%" mkdir "%MOD_PATH%"
if not exist "%MOD_PATH%\bin" mkdir "%MOD_PATH%\bin"
if not exist "%MOD_PATH%\bin\Win64_Shipping_Client" mkdir "%MOD_PATH%\bin\Win64_Shipping_Client"

echo 5. 复制文件到Mod目录...
copy "SubModule.xml" "%MOD_PATH%\" /Y
copy "bin\Win64_Shipping_Client\%PROJECT_NAME%.dll" "%MOD_PATH%\bin\Win64_Shipping_Client\" /Y

REM 复制LiteNetLib.dll
if exist "packages\LiteNetLib.1.3.1\lib\net471\LiteNetLib.dll" (
    copy "packages\LiteNetLib.1.3.1\lib\net471\LiteNetLib.dll" "%MOD_PATH%\bin\Win64_Shipping_Client\" /Y
) else (
    echo 警告: 找不到LiteNetLib.dll，请手动复制
)

echo.
echo ========================================
echo 编译和部署完成！
echo ========================================
echo Mod安装位置: %MOD_PATH%
echo.
echo 下一步操作:
echo 1. 启动骑砍2启动器
echo 2. 在模组列表中启用 "LAN Fight"
echo 3. 开始游戏测试
echo.
echo 按任意键退出...
pause >nul
