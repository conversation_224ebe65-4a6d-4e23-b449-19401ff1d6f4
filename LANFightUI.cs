using System;
using TaleWorlds.Core;
using TaleWorlds.Engine.GauntletUI;
using TaleWorlds.GauntletUI.Data;
using TaleWorlds.Library;
using TaleWorlds.Localization;
using TaleWorlds.MountAndBlade;
using TaleWorlds.ScreenSystem;

namespace LAN_Fight
{
    /// <summary>
    /// LAN Fight UI 管理器
    /// 提供简单的用户界面来创建和加入房间
    /// </summary>
    public class LANFightUI
    {
        private ConnectionManager _connectionManager;
        
        public LANFightUI(ConnectionManager connectionManager)
        {
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
        }

        /// <summary>
        /// 显示创建房间对话框
        /// </summary>
        public void ShowCreateRoomDialog()
        {
            try
            {
                // 使用简单的输入对话框
                InformationManager.ShowTextInquiry(
                    new TextInquiryData(
                        "创建房间",
                        "请输入房间名称:",
                        true,
                        true,
                        "创建",
                        "取消",
                        (roomName) => {
                            if (!string.IsNullOrEmpty(roomName))
                            {
                                CreateRoom(roomName);
                            }
                        },
                        null,
                        false,
                        null,
                        "LAN Fight Room"
                    )
                );
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示创建房间对话框失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 显示加入房间对话框
        /// </summary>
        public void ShowJoinRoomDialog()
        {
            try
            {
                // 使用简单的输入对话框
                InformationManager.ShowTextInquiry(
                    new TextInquiryData(
                        "加入房间",
                        "请输入服务器IP地址:",
                        true,
                        true,
                        "连接",
                        "取消",
                        (ipAddress) => {
                            if (!string.IsNullOrEmpty(ipAddress))
                            {
                                JoinRoom(ipAddress);
                            }
                        },
                        null,
                        false,
                        null,
                        "127.0.0.1"
                    )
                );
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示加入房间对话框失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 显示连接状态对话框
        /// </summary>
        public void ShowConnectionStatus()
        {
            try
            {
                string statusInfo = _connectionManager.GetConnectionInfo();
                
                InformationManager.ShowInquiry(
                    new InquiryData(
                        "连接状态",
                        statusInfo,
                        true,
                        _connectionManager.IsConnected,
                        "确定",
                        _connectionManager.IsConnected ? "断开连接" : null,
                        null,
                        () => {
                            if (_connectionManager.IsConnected)
                            {
                                _connectionManager.Disconnect();
                            }
                        }
                    )
                );
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示连接状态失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 显示房间设置对话框
        /// </summary>
        public void ShowRoomSettingsDialog()
        {
            try
            {
                if (!_connectionManager.IsHost)
                {
                    InformationManager.DisplayMessage(new InformationMessage("只有房主可以修改房间设置"));
                    return;
                }

                // 这里可以扩展更复杂的房间设置界面
                InformationManager.ShowInquiry(
                    new InquiryData(
                        "房间设置",
                        "房间设置功能正在开发中...",
                        true,
                        false,
                        "确定",
                        null,
                        null,
                        null
                    )
                );
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示房间设置失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 创建房间
        /// </summary>
        private void CreateRoom(string roomName)
        {
            try
            {
                bool success = _connectionManager.StartHost(roomName);
                
                if (success)
                {
                    InformationManager.DisplayMessage(new InformationMessage($"房间 '{roomName}' 创建成功！"));
                    
                    // 显示房间信息
                    ShowRoomInfo();
                }
                else
                {
                    InformationManager.DisplayMessage(new InformationMessage("创建房间失败，请检查网络设置"));
                }
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"创建房间时出错: {ex.Message}"));
            }
        }

        /// <summary>
        /// 加入房间
        /// </summary>
        private void JoinRoom(string ipAddress)
        {
            try
            {
                // 验证IP地址格式
                if (!IsValidIPAddress(ipAddress))
                {
                    InformationManager.DisplayMessage(new InformationMessage("IP地址格式不正确"));
                    return;
                }

                bool success = _connectionManager.JoinRoom(ipAddress);
                
                if (success)
                {
                    InformationManager.DisplayMessage(new InformationMessage($"正在连接到 {ipAddress}..."));
                }
                else
                {
                    InformationManager.DisplayMessage(new InformationMessage("连接失败，请检查IP地址和网络设置"));
                }
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"加入房间时出错: {ex.Message}"));
            }
        }

        /// <summary>
        /// 显示房间信息
        /// </summary>
        private void ShowRoomInfo()
        {
            try
            {
                if (!_connectionManager.RoomManager.IsInRoom)
                {
                    InformationManager.DisplayMessage(new InformationMessage("当前不在房间中"));
                    return;
                }

                var room = _connectionManager.RoomManager.CurrentRoom;
                string roomInfo = $"房间名称: {room.RoomName}\n" +
                                 $"玩家数量: {room.CurrentPlayerCount}/{room.MaxPlayers}\n" +
                                 $"房间状态: {room.State}\n" +
                                 $"创建时间: {room.CreateTime:HH:mm:ss}\n" +
                                 $"是否为主机: {(_connectionManager.IsHost ? "是" : "否")}";

                InformationManager.ShowInquiry(
                    new InquiryData(
                        "房间信息",
                        roomInfo,
                        true,
                        true,
                        "确定",
                        "离开房间",
                        null,
                        () => {
                            _connectionManager.Disconnect();
                        }
                    )
                );
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示房间信息失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 验证IP地址格式
        /// </summary>
        private bool IsValidIPAddress(string ipAddress)
        {
            if (string.IsNullOrEmpty(ipAddress))
                return false;

            // 简单的IP地址验证
            if (ipAddress == "localhost" || ipAddress == "127.0.0.1")
                return true;

            string[] parts = ipAddress.Split('.');
            if (parts.Length != 4)
                return false;

            foreach (string part in parts)
            {
                if (!int.TryParse(part, out int num) || num < 0 || num > 255)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 显示帮助信息
        /// </summary>
        public void ShowHelp()
        {
            try
            {
                string helpText = "LAN Fight Mod 使用说明:\n\n" +
                                 "1. 创建房间: 点击'创建局域网房间'来创建一个新房间\n" +
                                 "2. 加入房间: 点击'加入局域网房间'并输入主机的IP地址\n" +
                                 "3. 默认端口: 7777\n" +
                                 "4. 本地测试: 使用 127.0.0.1 或 localhost\n\n" +
                                 "注意事项:\n" +
                                 "- 确保防火墙允许程序通过\n" +
                                 "- 主机和客户端需要在同一局域网内\n" +
                                 "- 目前支持最多4个玩家同时游戏";

                InformationManager.ShowInquiry(
                    new InquiryData(
                        "使用帮助",
                        helpText,
                        true,
                        false,
                        "确定",
                        null,
                        null,
                        null
                    )
                );
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示帮助信息失败: {ex.Message}"));
            }
        }
    }
}
