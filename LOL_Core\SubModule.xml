<?xml version="1.0" encoding="utf-8"?>
<Module>
    <Name value="LOL_Core"/>
    <Id value="LOL_Core"/>
    <Version value="v1.2.0"/>
    <SingleplayerModule value="true"/>
    <MultiplayerModule value="false"/>
    <Official value="false"/>
    <DependedModules>
        <DependedModule Id="Native"/>
        <DependedModule Id="SandBoxCore"/>
        <DependedModule Id="Sandbox"/>
        <DependedModule Id="StoryMode"/>
    </DependedModules>
    <SubModules>
        <SubModule>
            <Name value="LOL_Core"/>
            <DLLName value="LOL_Core.dll"/>
            <SubModuleClassType value="LOL_Core.SubModule"/>
            <Tags>
                <Tag key="DedicatedServerType" value="none" />
                <Tag key="IsNoRenderModeElement" value="false" />
            </Tags>
        </SubModule>
    </SubModules>
    <Xmls>
    	<XmlNode>                
			<XmlName id="SPCultures" path="lol_cultures" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
				<GameType value="CustomGame" />
				<GameType value="EditorGame" />
			</IncludedGameTypes>
		</XmlNode> 
		<XmlNode>                
			<XmlName id="GameText" path="lol_strings" />
		</XmlNode> 
	</Xmls>
</Module> 