using System;
using System.Collections.Generic;
using System.Text;
using TaleWorlds.Library;
using TaleWorlds.MountAndBlade;

namespace LAN_Fight
{
    /// <summary>
    /// LAN Fight 调试器
    /// 提供调试和测试功能，帮助开发者诊断问题
    /// </summary>
    public class LANFightDebugger
    {
        private ConnectionManager _connectionManager;
        private List<string> _debugLogs;
        private bool _isDebugEnabled;
        private DateTime _lastLogTime;

        public bool IsDebugEnabled 
        { 
            get => _isDebugEnabled; 
            set => _isDebugEnabled = value; 
        }

        public LANFightDebugger(ConnectionManager connectionManager)
        {
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
            _debugLogs = new List<string>();
            _isDebugEnabled = true;
            _lastLogTime = DateTime.Now;

            // 订阅连接管理器事件进行调试
            SubscribeToEvents();
        }

        /// <summary>
        /// 订阅事件进行调试监控
        /// </summary>
        private void SubscribeToEvents()
        {
            _connectionManager.OnStateChanged += (oldState, newState) =>
            {
                LogDebug($"连接状态改变: {oldState} -> {newState}");
            };

            _connectionManager.OnStatusMessage += (message) =>
            {
                LogDebug($"状态消息: {message}");
            };

            _connectionManager.OnError += (error) =>
            {
                LogDebug($"错误: {error}");
            };
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        public void LogDebug(string message)
        {
            if (!_isDebugEnabled) return;

            try
            {
                string logEntry = $"[{DateTime.Now:HH:mm:ss.fff}] {message}";
                _debugLogs.Add(logEntry);
                _lastLogTime = DateTime.Now;

                // 限制日志数量，避免内存溢出
                if (_debugLogs.Count > 1000)
                {
                    _debugLogs.RemoveRange(0, 500);
                }

                Debug.Print($"[LAN Fight Debug] {logEntry}");
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight Debug] 记录日志时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取调试信息
        /// </summary>
        public string GetDebugInfo()
        {
            try
            {
                var info = new StringBuilder();
                info.AppendLine("=== LAN Fight 调试信息 ===");
                info.AppendLine($"调试状态: {(_isDebugEnabled ? "启用" : "禁用")}");
                info.AppendLine($"日志条数: {_debugLogs.Count}");
                info.AppendLine($"最后日志时间: {_lastLogTime:HH:mm:ss}");
                info.AppendLine();

                // 连接信息
                info.AppendLine("=== 连接信息 ===");
                info.AppendLine(_connectionManager.GetConnectionInfo());
                info.AppendLine();

                // 网络信息
                info.AppendLine("=== 网络信息 ===");
                info.AppendLine($"网络管理器运行: {_connectionManager.NetworkManager.IsRunning}");
                info.AppendLine($"连接玩家数: {_connectionManager.NetworkManager.ConnectedPlayersCount}");
                info.AppendLine();

                // 房间信息
                if (_connectionManager.RoomManager.IsInRoom)
                {
                    var room = _connectionManager.RoomManager.CurrentRoom;
                    info.AppendLine("=== 房间信息 ===");
                    info.AppendLine($"房间ID: {room.RoomId}");
                    info.AppendLine($"房间名称: {room.RoomName}");
                    info.AppendLine($"玩家数量: {room.CurrentPlayerCount}/{room.MaxPlayers}");
                    info.AppendLine($"房间状态: {room.State}");
                    info.AppendLine($"创建时间: {room.CreateTime}");
                    info.AppendLine($"是否有密码: {room.HasPassword}");
                    info.AppendLine();
                }

                // 最近的日志
                info.AppendLine("=== 最近日志 (最多20条) ===");
                int startIndex = Math.Max(0, _debugLogs.Count - 20);
                for (int i = startIndex; i < _debugLogs.Count; i++)
                {
                    info.AppendLine(_debugLogs[i]);
                }

                return info.ToString();
            }
            catch (Exception ex)
            {
                return $"获取调试信息时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 显示调试信息
        /// </summary>
        public void ShowDebugInfo()
        {
            try
            {
                string debugInfo = GetDebugInfo();
                
                InformationManager.ShowInquiry(
                    new InquiryData(
                        "调试信息",
                        debugInfo,
                        true,
                        true,
                        "确定",
                        "清除日志",
                        null,
                        () => {
                            ClearLogs();
                            InformationManager.DisplayMessage(new InformationMessage("调试日志已清除"));
                        }
                    )
                );
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示调试信息失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 清除调试日志
        /// </summary>
        public void ClearLogs()
        {
            _debugLogs.Clear();
            LogDebug("调试日志已清除");
        }

        /// <summary>
        /// 执行网络连接测试
        /// </summary>
        public void RunConnectionTest()
        {
            try
            {
                LogDebug("开始网络连接测试...");
                
                // 测试本地连接
                bool canCreateRoom = TestCreateRoom();
                bool canJoinRoom = TestJoinRoom();
                
                string testResult = $"连接测试结果:\n" +
                                   $"创建房间: {(canCreateRoom ? "成功" : "失败")}\n" +
                                   $"加入房间: {(canJoinRoom ? "成功" : "失败")}\n\n" +
                                   "详细信息请查看调试日志";

                InformationManager.ShowInquiry(
                    new InquiryData(
                        "连接测试结果",
                        testResult,
                        true,
                        false,
                        "确定",
                        null,
                        null,
                        null
                    )
                );

                LogDebug("网络连接测试完成");
            }
            catch (Exception ex)
            {
                LogDebug($"网络连接测试失败: {ex.Message}");
                InformationManager.DisplayMessage(new InformationMessage($"连接测试失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 测试创建房间
        /// </summary>
        private bool TestCreateRoom()
        {
            try
            {
                LogDebug("测试创建房间...");
                
                if (_connectionManager.IsConnected)
                {
                    LogDebug("已在连接状态，跳过创建房间测试");
                    return false;
                }

                // 这里只是模拟测试，不实际创建房间
                LogDebug("创建房间测试: 网络配置检查通过");
                return true;
            }
            catch (Exception ex)
            {
                LogDebug($"创建房间测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试加入房间
        /// </summary>
        private bool TestJoinRoom()
        {
            try
            {
                LogDebug("测试加入房间...");
                
                if (_connectionManager.IsConnected)
                {
                    LogDebug("已在连接状态，跳过加入房间测试");
                    return false;
                }

                // 这里只是模拟测试，不实际加入房间
                LogDebug("加入房间测试: 网络配置检查通过");
                return true;
            }
            catch (Exception ex)
            {
                LogDebug($"加入房间测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取系统信息
        /// </summary>
        public string GetSystemInfo()
        {
            try
            {
                var info = new StringBuilder();
                info.AppendLine("=== 系统信息 ===");
                info.AppendLine($"操作系统: {Environment.OSVersion}");
                info.AppendLine($"运行时版本: {Environment.Version}");
                info.AppendLine($"机器名称: {Environment.MachineName}");
                info.AppendLine($"用户名: {Environment.UserName}");
                info.AppendLine($"当前时间: {DateTime.Now}");
                info.AppendLine();

                info.AppendLine("=== 游戏信息 ===");
                info.AppendLine($"游戏版本: {ApplicationVersion.FromParametersFile().ToString()}");
                info.AppendLine($"模块数量: {Module.CurrentModule?.SubModules?.Count ?? 0}");
                info.AppendLine();

                return info.ToString();
            }
            catch (Exception ex)
            {
                return $"获取系统信息时出错: {ex.Message}";
            }
        }

        /// <summary>
        /// 显示完整的诊断报告
        /// </summary>
        public void ShowDiagnosticReport()
        {
            try
            {
                var report = new StringBuilder();
                report.AppendLine(GetSystemInfo());
                report.AppendLine(GetDebugInfo());

                InformationManager.ShowInquiry(
                    new InquiryData(
                        "诊断报告",
                        report.ToString(),
                        true,
                        false,
                        "确定",
                        null,
                        null,
                        null
                    )
                );
            }
            catch (Exception ex)
            {
                InformationManager.DisplayMessage(new InformationMessage($"显示诊断报告失败: {ex.Message}"));
            }
        }
    }
}
