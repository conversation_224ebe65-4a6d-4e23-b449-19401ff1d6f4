using System;
using LiteNetLib.Utils;

namespace LAN_Fight
{
    /// <summary>
    /// 网络消息类型枚举
    /// </summary>
    public enum NetworkMessageType : byte
    {
        PlayerJoin = 1,
        PlayerLeave = 2,
        ChatMessage = 3,
        GameStateSync = 4,
        PlayerAction = 5,
        RoomInfo = 6,
        Heartbeat = 7
    }

    /// <summary>
    /// 网络消息基类
    /// </summary>
    public abstract class NetworkMessage : INetSerializable
    {
        public NetworkMessageType MessageType { get; protected set; }
        public DateTime Timestamp { get; set; }

        protected NetworkMessage(NetworkMessageType messageType)
        {
            MessageType = messageType;
            Timestamp = DateTime.Now;
        }

        public abstract void Serialize(NetDataWriter writer);
        public abstract void Deserialize(NetDataReader reader);
    }

    /// <summary>
    /// 玩家加入消息
    /// </summary>
    public class PlayerJoinMessage : NetworkMessage
    {
        public string PlayerName { get; set; }
        public string PlayerId { get; set; }

        public PlayerJoinMessage() : base(NetworkMessageType.PlayerJoin)
        {
        }

        public PlayerJoinMessage(string playerName, string playerId) : base(NetworkMessageType.PlayerJoin)
        {
            PlayerName = playerName;
            PlayerId = playerId;
        }

        public override void Serialize(NetDataWriter writer)
        {
            writer.Put((byte)MessageType);
            writer.Put(PlayerName ?? "");
            writer.Put(PlayerId ?? "");
            writer.Put(Timestamp.ToBinary());
        }

        public override void Deserialize(NetDataReader reader)
        {
            PlayerName = reader.GetString();
            PlayerId = reader.GetString();
            Timestamp = DateTime.FromBinary(reader.GetLong());
        }
    }

    /// <summary>
    /// 玩家离开消息
    /// </summary>
    public class PlayerLeaveMessage : NetworkMessage
    {
        public string PlayerId { get; set; }
        public string Reason { get; set; }

        public PlayerLeaveMessage() : base(NetworkMessageType.PlayerLeave)
        {
        }

        public PlayerLeaveMessage(string playerId, string reason) : base(NetworkMessageType.PlayerLeave)
        {
            PlayerId = playerId;
            Reason = reason;
        }

        public override void Serialize(NetDataWriter writer)
        {
            writer.Put((byte)MessageType);
            writer.Put(PlayerId ?? "");
            writer.Put(Reason ?? "");
            writer.Put(Timestamp.ToBinary());
        }

        public override void Deserialize(NetDataReader reader)
        {
            PlayerId = reader.GetString();
            Reason = reader.GetString();
            Timestamp = DateTime.FromBinary(reader.GetLong());
        }
    }

    /// <summary>
    /// 聊天消息
    /// </summary>
    public class ChatMessage : NetworkMessage
    {
        public string SenderId { get; set; }
        public string SenderName { get; set; }
        public string Content { get; set; }

        public ChatMessage() : base(NetworkMessageType.ChatMessage)
        {
        }

        public ChatMessage(string senderId, string senderName, string content) : base(NetworkMessageType.ChatMessage)
        {
            SenderId = senderId;
            SenderName = senderName;
            Content = content;
        }

        public override void Serialize(NetDataWriter writer)
        {
            writer.Put((byte)MessageType);
            writer.Put(SenderId ?? "");
            writer.Put(SenderName ?? "");
            writer.Put(Content ?? "");
            writer.Put(Timestamp.ToBinary());
        }

        public override void Deserialize(NetDataReader reader)
        {
            SenderId = reader.GetString();
            SenderName = reader.GetString();
            Content = reader.GetString();
            Timestamp = DateTime.FromBinary(reader.GetLong());
        }
    }

    /// <summary>
    /// 房间信息消息
    /// </summary>
    public class RoomInfoMessage : NetworkMessage
    {
        public string RoomName { get; set; }
        public int CurrentPlayers { get; set; }
        public int MaxPlayers { get; set; }
        public bool HasPassword { get; set; }

        public RoomInfoMessage() : base(NetworkMessageType.RoomInfo)
        {
        }

        public RoomInfoMessage(string roomName, int currentPlayers, int maxPlayers, bool hasPassword) 
            : base(NetworkMessageType.RoomInfo)
        {
            RoomName = roomName;
            CurrentPlayers = currentPlayers;
            MaxPlayers = maxPlayers;
            HasPassword = hasPassword;
        }

        public override void Serialize(NetDataWriter writer)
        {
            writer.Put((byte)MessageType);
            writer.Put(RoomName ?? "");
            writer.Put(CurrentPlayers);
            writer.Put(MaxPlayers);
            writer.Put(HasPassword);
            writer.Put(Timestamp.ToBinary());
        }

        public override void Deserialize(NetDataReader reader)
        {
            RoomName = reader.GetString();
            CurrentPlayers = reader.GetInt();
            MaxPlayers = reader.GetInt();
            HasPassword = reader.GetBool();
            Timestamp = DateTime.FromBinary(reader.GetLong());
        }
    }

    /// <summary>
    /// 心跳消息
    /// </summary>
    public class HeartbeatMessage : NetworkMessage
    {
        public string PlayerId { get; set; }

        public HeartbeatMessage() : base(NetworkMessageType.Heartbeat)
        {
        }

        public HeartbeatMessage(string playerId) : base(NetworkMessageType.Heartbeat)
        {
            PlayerId = playerId;
        }

        public override void Serialize(NetDataWriter writer)
        {
            writer.Put((byte)MessageType);
            writer.Put(PlayerId ?? "");
            writer.Put(Timestamp.ToBinary());
        }

        public override void Deserialize(NetDataReader reader)
        {
            PlayerId = reader.GetString();
            Timestamp = DateTime.FromBinary(reader.GetLong());
        }
    }

    /// <summary>
    /// 网络消息工厂类
    /// </summary>
    public static class NetworkMessageFactory
    {
        /// <summary>
        /// 从数据读取器创建消息
        /// </summary>
        public static NetworkMessage CreateMessage(NetDataReader reader)
        {
            var messageType = (NetworkMessageType)reader.GetByte();

            NetworkMessage message;
            switch (messageType)
            {
                case NetworkMessageType.PlayerJoin:
                    message = new PlayerJoinMessage();
                    break;
                case NetworkMessageType.PlayerLeave:
                    message = new PlayerLeaveMessage();
                    break;
                case NetworkMessageType.ChatMessage:
                    message = new ChatMessage();
                    break;
                case NetworkMessageType.RoomInfo:
                    message = new RoomInfoMessage();
                    break;
                case NetworkMessageType.Heartbeat:
                    message = new HeartbeatMessage();
                    break;
                default:
                    throw new ArgumentException($"未知的消息类型: {messageType}");
            }

            message.Deserialize(reader);
            return message;
        }

        /// <summary>
        /// 将消息序列化到数据写入器
        /// </summary>
        public static void SerializeMessage(NetworkMessage message, NetDataWriter writer)
        {
            message.Serialize(writer);
        }
    }
}
