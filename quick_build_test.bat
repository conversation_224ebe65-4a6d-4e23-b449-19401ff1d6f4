@echo off
echo ========================================
echo LAN Fight Mod 快速编译测试
echo ========================================
echo.

set PROJECT_NAME=LAN_Fight
set MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe

REM 检查MSBuild路径
if not exist "%MSBUILD_PATH%" (
    echo 尝试查找其他MSBuild路径...
    
    REM 尝试VS2022路径
    set MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe
    if exist "%MSBUILD_PATH%" (
        echo 找到VS2022 MSBuild: %MSBUILD_PATH%
        goto :build
    )
    
    REM 尝试VS2019 Professional路径
    set MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe
    if exist "%MSBUILD_PATH%" (
        echo 找到VS2019 Professional MSBuild: %MSBUILD_PATH%
        goto :build
    )
    
    REM 尝试VS2022 Professional路径
    set MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe
    if exist "%MSBUILD_PATH%" (
        echo 找到VS2022 Professional MSBuild: %MSBUILD_PATH%
        goto :build
    )
    
    echo 错误: 找不到MSBuild.exe
    echo 请确保已安装Visual Studio 2019或2022
    echo.
    echo 或者手动修改此脚本中的MSBUILD_PATH变量
    pause
    exit /b 1
)

:build
echo 使用MSBuild路径: %MSBUILD_PATH%
echo.

echo 1. 清理旧的编译文件...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo 2. 编译项目 (仅测试编译，不部署)...
echo 编译命令: "%MSBUILD_PATH%" "%PROJECT_NAME%.csproj" /p:Configuration=Release /p:Platform=x64 /p:LangVersion=8.0
echo.

"%MSBUILD_PATH%" "%PROJECT_NAME%.csproj" /p:Configuration=Release /p:Platform=x64 /p:LangVersion=8.0

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo 编译失败！
    echo ========================================
    echo.
    echo 常见问题和解决方案:
    echo 1. 检查游戏路径是否正确
    echo 2. 确保所有骑砍2 DLL引用正确
    echo 3. 检查LiteNetLib包是否已安装
    echo 4. 确保.NET Framework 4.7.2已安装
    echo.
    echo 详细错误信息请查看上方输出
    pause
    exit /b 1
)

echo.
echo ========================================
echo 编译成功！
echo ========================================
echo.

echo 3. 检查编译输出...
if exist "bin\Win64_Shipping_Client\%PROJECT_NAME%.dll" (
    echo [✓] 找到主DLL: bin\Win64_Shipping_Client\%PROJECT_NAME%.dll
    
    REM 获取文件大小
    for %%A in ("bin\Win64_Shipping_Client\%PROJECT_NAME%.dll") do (
        echo     文件大小: %%~zA 字节
        echo     修改时间: %%~tA
    )
) else (
    echo [✗] 未找到编译输出文件
    echo 预期位置: bin\Win64_Shipping_Client\%PROJECT_NAME%.dll
)

echo.
echo 4. 检查依赖项...
if exist "packages\LiteNetLib.1.3.1\lib\net471\LiteNetLib.dll" (
    echo [✓] LiteNetLib.dll 可用
) else (
    echo [✗] LiteNetLib.dll 未找到
    echo 请在Visual Studio中安装LiteNetLib NuGet包
)

echo.
echo ========================================
echo 测试完成
echo ========================================
echo.
echo 如果编译成功，您可以运行 build_and_deploy.bat 进行完整部署
echo.
pause
