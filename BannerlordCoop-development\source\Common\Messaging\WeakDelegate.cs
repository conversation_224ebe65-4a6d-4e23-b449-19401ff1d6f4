﻿using System;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Common.Messaging
{
    /// <summary>
    /// Delegate that does not hold a strong reference to the target
    /// </summary>
    public class WeakDelegate
    {
        public bool IsAlive => reference.IsAlive;
        public object Instance => reference.Target;

        private WeakReference reference;
        private MethodInfo method;

        public WeakDelegate(Delegate @delegate)
        {
            reference = new WeakReference(@delegate.Target);
            method = @delegate.Method;
        }

        public T Invoke<T>(object[] parameters)
        {
            return (T)Invoke(parameters);
        }

        public object Invoke(object[] parameters)
        {
            var obj = reference.Target;

            if (obj == null) return null;

            return method.Invoke(obj, parameters);
        }

        public override bool Equals(object obj)
        {
            if (obj is WeakDelegate weakDelegate == false) return false;

            if (weakDelegate.method != method) return false;
            if (weakDelegate.reference.Target != reference.Target) return false;

            return true;
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        private Delegate ToDelegate()
        {
            return Delegate.CreateDelegate(reference.Target.GetType(), method);
        }

        public static implicit operator WeakDelegate(Delegate d) => new WeakDelegate(d);
        public static implicit operator Delegate(WeakDelegate d) => d.ToDelegate();
    }
}
