using System;

namespace LAN_Fight
{
    /// <summary>
    /// 网络配置类
    /// 包含网络连接的各种配置参数
    /// </summary>
    public class NetworkConfiguration
    {
        /// <summary>
        /// 默认端口号
        /// </summary>
        public const int DEFAULT_PORT = 7777;

        /// <summary>
        /// 连接密钥
        /// </summary>
        public const string CONNECTION_KEY = "LANFightMod";

        /// <summary>
        /// 最大连接数
        /// </summary>
        public const int MAX_CONNECTIONS = 4;

        /// <summary>
        /// 连接超时时间（毫秒）
        /// </summary>
        public const int CONNECTION_TIMEOUT = 30000;

        /// <summary>
        /// 心跳间隔（毫秒）
        /// </summary>
        public const int PING_INTERVAL = 5000;

        /// <summary>
        /// 重连延迟（毫秒）
        /// </summary>
        public const int RECONNECT_DELAY = 3000;

        /// <summary>
        /// 服务器地址
        /// </summary>
        public string ServerAddress { get; set; } = "127.0.0.1";

        /// <summary>
        /// 服务器端口
        /// </summary>
        public int ServerPort { get; set; } = DEFAULT_PORT;

        /// <summary>
        /// 是否为主机模式
        /// </summary>
        public bool IsHost { get; set; } = false;

        /// <summary>
        /// 玩家名称
        /// </summary>
        public string PlayerName { get; set; } = "Player";

        /// <summary>
        /// 房间名称
        /// </summary>
        public string RoomName { get; set; } = "LAN Fight Room";

        /// <summary>
        /// 房间密码（可选）
        /// </summary>
        public string RoomPassword { get; set; } = "";

        /// <summary>
        /// 是否启用调试模式
        /// </summary>
        public bool DebugMode { get; set; } = true;

        public NetworkConfiguration()
        {
        }

        public NetworkConfiguration(string serverAddress, int serverPort = DEFAULT_PORT)
        {
            ServerAddress = serverAddress;
            ServerPort = serverPort;
        }

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        /// <returns>配置是否有效</returns>
        public bool IsValid()
        {
            if (string.IsNullOrEmpty(ServerAddress))
                return false;

            if (ServerPort <= 0 || ServerPort > 65535)
                return false;

            if (string.IsNullOrEmpty(PlayerName))
                return false;

            return true;
        }

        /// <summary>
        /// 获取配置的字符串表示
        /// </summary>
        /// <returns>配置字符串</returns>
        public override string ToString()
        {
            return $"NetworkConfig[{ServerAddress}:{ServerPort}, Host:{IsHost}, Player:{PlayerName}, Room:{RoomName}]";
        }
    }
}
