using System;
using TaleWorlds.Core;
using TaleWorlds.Library;
using TaleWorlds.Localization;
using TaleWorlds.MountAndBlade;
using TaleWorlds.ScreenSystem;
using HarmonyLib;

namespace LAN_Fight
{
    /// <summary>
    /// LAN Fight Mod 主模块类
    /// 提供局域网联机功能，允许玩家1创建房间，玩家2加入房间
    /// </summary>
    public class LANFightSubModule : MBSubModuleBase
    {
        private Harmony _harmony;
        private ConnectionManager _connectionManager;

        // 菜单选项
        private InitialStateOption _hostGameOption;
        private InitialStateOption _joinGameOption;

        protected override void OnSubModuleLoad()
        {
            base.OnSubModuleLoad();
            
            // 初始化Harmony
            _harmony = new Harmony("lan_fight_mod");
            _harmony.PatchAll();

            // 初始化连接管理器
            _connectionManager = new ConnectionManager();

            // 订阅连接事件
            _connectionManager.OnStatusMessage += (message) =>
            {
                InformationManager.DisplayMessage(new InformationMessage(message));
            };

            _connectionManager.OnError += (error) =>
            {
                InformationManager.DisplayMessage(new InformationMessage($"错误: {error}"));
            };

            InformationManager.DisplayMessage(new InformationMessage("LAN Fight Mod 已加载"));
        }

        protected override void OnSubModuleUnloaded()
        {
            base.OnSubModuleUnloaded();
            
            // 清理资源
            _connectionManager?.Dispose();
            _harmony?.UnpatchAll("lan_fight_mod");
        }

        protected override void OnBeforeInitialModuleScreenSetAsRoot()
        {
            base.OnBeforeInitialModuleScreenSetAsRoot();
            
            // 添加主菜单选项
            AddMainMenuOptions();
        }

        protected override void OnApplicationTick(float dt)
        {
            base.OnApplicationTick(dt);
            
            // 更新连接管理器
            _connectionManager?.Update(TimeSpan.FromSeconds(dt));
        }

        /// <summary>
        /// 添加主菜单选项
        /// </summary>
        private void AddMainMenuOptions()
        {
            // 创建房间选项
            _hostGameOption = new InitialStateOption(
                "LANFightHost",
                new TextObject("创建局域网房间"),
                9990,
                () => {
                    try
                    {
                        _connectionManager.StartHost();
                    }
                    catch (Exception ex)
                    {
                        InformationManager.DisplayMessage(new InformationMessage($"创建房间失败: {ex.Message}"));
                    }
                },
                () => (false, new TextObject())
            );

            // 加入房间选项
            _joinGameOption = new InitialStateOption(
                "LANFightJoin",
                new TextObject("加入局域网房间"),
                9991,
                () => {
                    try
                    {
                        // 这里应该打开一个UI让用户输入IP地址
                        // 暂时使用默认的localhost进行测试
                        _connectionManager.JoinRoom("127.0.0.1");
                    }
                    catch (Exception ex)
                    {
                        InformationManager.DisplayMessage(new InformationMessage($"连接房间失败: {ex.Message}"));
                    }
                },
                () => (false, new TextObject())
            );

            // 添加到主菜单
            Module.CurrentModule.AddInitialStateOption(_hostGameOption);
            Module.CurrentModule.AddInitialStateOption(_joinGameOption);
        }

        public override void OnGameEnd(Game game)
        {
            base.OnGameEnd(game);
            
            // 游戏结束时断开网络连接
            _connectionManager?.Disconnect();
        }
    }
}
