using System;
using TaleWorlds.InputSystem;
using TaleWorlds.Library;
using TaleWorlds.MountAndBlade;

namespace LAN_Fight
{
    /// <summary>
    /// LAN Fight 快捷键管理器
    /// 提供游戏内快捷键来快速访问联机功能
    /// </summary>
    public class LANFightHotkeys
    {
        private ConnectionManager _connectionManager;
        private LANFightUI _ui;
        
        // 快捷键定义
        private const InputKey CREATE_ROOM_KEY = InputKey.F9;
        private const InputKey JOIN_ROOM_KEY = InputKey.F10;
        private const InputKey CONNECTION_STATUS_KEY = InputKey.F11;
        private const InputKey HELP_KEY = InputKey.F12;

        private bool _isEnabled = true;
        private DateTime _lastKeyPress = DateTime.MinValue;
        private const int KEY_PRESS_COOLDOWN = 500; // 毫秒

        public bool IsEnabled 
        { 
            get => _isEnabled; 
            set => _isEnabled = value; 
        }

        public LANFightHotkeys(ConnectionManager connectionManager, LANFightUI ui)
        {
            _connectionManager = connectionManager ?? throw new ArgumentNullException(nameof(connectionManager));
            _ui = ui ?? throw new ArgumentNullException(nameof(ui));
        }

        /// <summary>
        /// 更新快捷键检测
        /// </summary>
        public void Update()
        {
            if (!_isEnabled) return;

            try
            {
                // 检查按键冷却时间
                if ((DateTime.Now - _lastKeyPress).TotalMilliseconds < KEY_PRESS_COOLDOWN)
                    return;

                // 检查各种快捷键
                if (Input.IsKeyPressed(CREATE_ROOM_KEY))
                {
                    HandleCreateRoomKey();
                }
                else if (Input.IsKeyPressed(JOIN_ROOM_KEY))
                {
                    HandleJoinRoomKey();
                }
                else if (Input.IsKeyPressed(CONNECTION_STATUS_KEY))
                {
                    HandleConnectionStatusKey();
                }
                else if (Input.IsKeyPressed(HELP_KEY))
                {
                    HandleHelpKey();
                }
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 快捷键更新时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理创建房间快捷键
        /// </summary>
        private void HandleCreateRoomKey()
        {
            try
            {
                _lastKeyPress = DateTime.Now;

                if (_connectionManager.IsConnected)
                {
                    InformationManager.DisplayMessage(new InformationMessage("已在连接状态，无法创建新房间"));
                    return;
                }

                _ui.ShowCreateRoomDialog();
                Debug.Print("[LAN Fight] 快捷键: 创建房间");
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 处理创建房间快捷键时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理加入房间快捷键
        /// </summary>
        private void HandleJoinRoomKey()
        {
            try
            {
                _lastKeyPress = DateTime.Now;

                if (_connectionManager.IsConnected)
                {
                    InformationManager.DisplayMessage(new InformationMessage("已在连接状态，无法加入新房间"));
                    return;
                }

                _ui.ShowJoinRoomDialog();
                Debug.Print("[LAN Fight] 快捷键: 加入房间");
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 处理加入房间快捷键时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理连接状态快捷键
        /// </summary>
        private void HandleConnectionStatusKey()
        {
            try
            {
                _lastKeyPress = DateTime.Now;
                _ui.ShowConnectionStatus();
                Debug.Print("[LAN Fight] 快捷键: 连接状态");
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 处理连接状态快捷键时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理帮助快捷键
        /// </summary>
        private void HandleHelpKey()
        {
            try
            {
                _lastKeyPress = DateTime.Now;
                _ui.ShowHelp();
                Debug.Print("[LAN Fight] 快捷键: 帮助");
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 处理帮助快捷键时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示快捷键提示
        /// </summary>
        public void ShowHotkeyHints()
        {
            try
            {
                string hints = "LAN Fight 快捷键:\n\n" +
                              $"F9  - 创建房间\n" +
                              $"F10 - 加入房间\n" +
                              $"F11 - 连接状态\n" +
                              $"F12 - 帮助信息\n\n" +
                              "提示: 在游戏中按相应快捷键即可快速访问联机功能";

                InformationManager.ShowInquiry(
                    new InquiryData(
                        "快捷键说明",
                        hints,
                        true,
                        false,
                        "确定",
                        null,
                        null,
                        null
                    )
                );
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 显示快捷键提示时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 启用快捷键
        /// </summary>
        public void Enable()
        {
            _isEnabled = true;
            InformationManager.DisplayMessage(new InformationMessage("LAN Fight 快捷键已启用"));
            Debug.Print("[LAN Fight] 快捷键已启用");
        }

        /// <summary>
        /// 禁用快捷键
        /// </summary>
        public void Disable()
        {
            _isEnabled = false;
            InformationManager.DisplayMessage(new InformationMessage("LAN Fight 快捷键已禁用"));
            Debug.Print("[LAN Fight] 快捷键已禁用");
        }

        /// <summary>
        /// 切换快捷键启用状态
        /// </summary>
        public void Toggle()
        {
            if (_isEnabled)
                Disable();
            else
                Enable();
        }

        /// <summary>
        /// 获取快捷键状态信息
        /// </summary>
        public string GetStatusInfo()
        {
            return $"快捷键状态: {(_isEnabled ? "启用" : "禁用")}\n" +
                   $"上次按键时间: {(_lastKeyPress == DateTime.MinValue ? "无" : _lastKeyPress.ToString("HH:mm:ss"))}\n" +
                   $"按键冷却时间: {KEY_PRESS_COOLDOWN}ms";
        }
    }
}
