<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="852px" height="931px" viewBox="-0.5 -0.5 852 931" content="&lt;mxfile host=&quot;78f5389e-dabf-4c74-b90e-3b0aaff93f4f&quot; modified=&quot;2020-09-26T10:30:56.146Z&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.49.1 Chrome/83.0.4103.122 Electron/9.2.1 Safari/537.36&quot; etag=&quot;d5-vAS5m92WrjzpqpU0b&quot; version=&quot;13.1.3&quot;&gt;&lt;diagram id=&quot;poV7IlEdQqrj2gPoaiXM&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 0 120 L 40 120 L 40 134 L 850 134 L 850 500 L 0 500 L 0 134 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 0 134 L 40 134" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 1px; height: 1px; padding-top: 137px; margin-left: 2px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: left; ">
                            <div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                Persistence.Party
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="2" y="151" fill="#000000" font-family="Helvetica" font-size="14px" font-weight="bold">
                    Persistence.Party
                </text>
            </switch>
        </g>
        <path d="M 130 425 L 130 356.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 130 351.12 L 133.5 358.12 L 130 356.37 L 126.5 358.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 390px; margin-left: 130px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;observes&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="130" y="394" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;observes&gt;
                </text>
            </switch>
        </g>
        <path d="M 230 455.03 L 360.03 455.03 L 360.03 426.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 360.03 421.12 L 363.53 428.12 L 360.03 426.37 L 356.53 428.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 455px; margin-left: 298px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;generates&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="298" y="459" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;generates&gt;
                </text>
            </switch>
        </g>
        <path d="M 130.03 485 L 130.03 600 L 323.63 600" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 328.88 600 L 321.88 603.5 L 323.63 600 L 321.88 596.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 600px; margin-left: 228px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;registerInstanceHandler&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="228" y="604" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;registerInstanceHandler&gt;
                </text>
            </switch>
        </g>
        <rect x="30" y="425" width="200" height="60" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 455px; margin-left: 130px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                <font face="Lucida Console">
                                    &lt;MobilePartyEntityClient&gt;
                                </font>
                                <br/>
                                player0
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="130" y="459" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;MobilePartyEntityClient&gt;...
                </text>
            </switch>
        </g>
        <path d="M 570 45.03 L 730.03 45.03 L 730 283.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 730 288.88 L 726.5 281.88 L 730 283.63 L 733.5 281.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 80px; margin-left: 728px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;observes&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="728" y="84" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;observes&gt;
                </text>
            </switch>
        </g>
        <path d="M 290 45.03 L 130.03 45.03 L 130 283.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 130 288.88 L 126.5 281.88 L 130 283.63 L 133.5 281.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 91px; margin-left: 130px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;writes&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="130" y="95" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;writes&gt;
                </text>
            </switch>
        </g>
        <path d="M 500 90 L 500 353.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 500 358.88 L 496.5 351.88 L 500 353.63 L 503.5 351.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="290" y="0" width="280" height="90" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 45px; margin-left: 430px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                Railgun
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="49" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Railgun
                </text>
            </switch>
        </g>
        <path d="M 547 7 L 563 7 L 563 27 L 547 27 L 547 23 L 543 23 L 543 19 L 547 19 L 547 15 L 543 15 L 543 11 L 547 11 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 547 11 L 551 11 L 551 15 L 547 15 M 547 19 L 551 19 L 551 23 L 547 23" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 730 425 L 730 356.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 730 351.12 L 733.5 358.12 L 730 356.37 L 726.5 358.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 390px; margin-left: 730px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;updates&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="730" y="393" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;updates&gt;
                </text>
            </switch>
        </g>
        <path d="M 730.03 485 L 730.03 600 L 536.37 600" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 531.12 600 L 538.12 596.5 L 536.37 600 L 538.12 603.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 599px; margin-left: 622px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;registerInstanceHandler&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="622" y="603" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;registerInstanceHandler&gt;
                </text>
            </switch>
        </g>
        <rect x="630" y="425" width="200" height="60" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 455px; margin-left: 730px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                <font face="Lucida Console">
                                    &lt;MobilePartyEntityServer&gt;
                                </font>
                                <br/>
                                player0
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="730" y="459" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;MobilePartyEntityServer&gt;...
                </text>
            </switch>
        </g>
        <rect x="630" y="290" width="200" height="60" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 320px; margin-left: 730px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                <font face="Lucida Console">
                                    &lt;MobilePartyState&gt;
                                </font>
                                <br/>
                                player0
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="730" y="324" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;MobilePartyState&gt;...
                </text>
            </switch>
        </g>
        <rect x="30" y="290" width="200" height="60" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 320px; margin-left: 130px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                <font face="Lucida Console">
                                    &lt;MobilePartyState&gt;
                                </font>
                                <br/>
                                player0
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="130" y="324" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;MobilePartyState&gt;...
                </text>
            </switch>
        </g>
        <path d="M 761.43 265 L 747.14 290" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 740 165 L 828 165 L 840 177 L 840 265 L 740 265 L 740 165 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 828 165 L 828 177 L 840 177" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 215px; margin-left: 741px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                The server side state is refered to as "authoritative".
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="790" y="219" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    The server side...
                </text>
            </switch>
        </g>
        <path d="M 185 265 L 160 290" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 150 165 L 308 165 L 320 177 L 320 265 L 150 265 L 150 165 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 308 165 L 308 177 L 320 177" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 168px; height: 1px; padding-top: 215px; margin-left: 151px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                The client side state always equals the authoritative state. This is done automatically by Railgun
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="235" y="219" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    The client side state always...
                </text>
            </switch>
        </g>
        <path d="M 360 360 L 360 96.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 360 91.12 L 363.5 98.12 L 360 96.37 L 356.5 98.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="300" y="360" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 390px; margin-left: 360px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                <font face="Lucida Console">
                                    &lt;EventPartyMove&gt;
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="360" y="394" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;EventPartyMove&gt;
                </text>
            </switch>
        </g>
        <path d="M 560 390 L 595 390 L 595 320 L 623.63 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 628.88 320 L 621.88 323.5 L 623.63 320 L 621.88 316.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 370px; margin-left: 597px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;modifies&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="597" y="373" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;modifies&gt;
                </text>
            </switch>
        </g>
        <rect x="440" y="360" width="120" height="60" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 390px; margin-left: 500px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                <font face="Lucida Console">
                                    &lt;EventPartyMove&gt;
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="500" y="394" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;EventPartyMove&gt;
                </text>
            </switch>
        </g>
        <path d="M 360 90 L 360.03 70 L 363 70 L 500.03 70 L 500.01 83.63" fill="none" stroke="#000000" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/>
        <path d="M 500 88.88 L 496.51 81.88 L 500.01 83.63 L 503.51 81.89 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="330" y="560" width="200" height="80" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 600px; margin-left: 430px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                <font face="Lucida Console">
                                    &lt;FieldAccessGroup&gt;
                                    <br/>
                                    Patch.
                                    <br/>
                                    CampaignMapMovement.
                                    <br/>
                                    Movement
                                    <br/>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="604" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;FieldAccessGroup&gt;...
                </text>
            </switch>
        </g>
        <rect x="330" y="860" width="200" height="60" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 890px; margin-left: 430px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                <font face="Lucida Console">
                                    &lt;FieldChangeBuffer&gt;
                                    <br/>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="894" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;FieldChangeBuffer&gt;&#xa;
                </text>
            </switch>
        </g>
        <path d="M 161.25 820 L 180 850" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 55 720 L 193 720 L 205 732 L 205 820 L 55 820 L 55 720 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 193 720 L 193 732 L 205 732" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 770px; margin-left: 56px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                The Movement patch intercepts all setters to movement data and instead writes it to a global buffer. The underlying data will not be changed.
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="130" y="774" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    The Movement patch interc...
                </text>
            </switch>
        </g>
        <path d="M 430 790 L 430 853.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 430 858.88 L 426.5 851.88 L 430 853.63 L 433.5 851.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 818px; margin-left: 430px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;processesRequests&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="821" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;processesRequests&gt;
                </text>
            </switch>
        </g>
        <path d="M 430 730 L 430 646.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 430 641.12 L 433.5 648.12 L 430 646.37 L 426.5 648.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 681px; margin-left: 430px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;invokeHandlers&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="684" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;invokeHandlers&gt;
                </text>
            </switch>
        </g>
        <rect x="330" y="730" width="200" height="60" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 760px; margin-left: 430px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                <font face="Lucida Console">
                                    &lt;PersistenceClient&gt;
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="430" y="764" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;PersistenceClient&gt;
                </text>
            </switch>
        </g>
        <path d="M 230 890 L 323.63 890" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 328.88 890 L 321.88 893.5 L 323.63 890 L 321.88 886.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 890px; margin-left: 281px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; background-color: #ffffff; white-space: nowrap; ">
                                &lt;writesTo&gt;
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="281" y="894" fill="#000000" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    &lt;writesTo&gt;
                </text>
            </switch>
        </g>
        <rect x="30" y="850" width="200" height="80" fill="#ffffff" stroke="#000000" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 890px; margin-left: 130px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: nowrap; ">
                                <font face="Lucida Console">
                                    &lt;MobilePartyPatch&gt;
                                    <br/>
                                    Patch.
                                    <br/>
                                    CampaignMapMovement.
                                    <br/>
                                    MobilePartyPatch
                                    <br/>
                                </font>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="130" y="894" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    &lt;MobilePartyPatch&gt;...
                </text>
            </switch>
        </g>
        <path d="M 426.43 550 L 427.14 560" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 227.5 510 L 610.5 510 L 622.5 522 L 622.5 550 L 227.5 550 L 227.5 510 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 610.5 510 L 610.5 522 L 622.5 522" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 393px; height: 1px; padding-top: 530px; margin-left: 228px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                The Movement patch is an interceptor for the underlying movement data fields. It triggers a callback when the values are changed.
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="425" y="534" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    The Movement patch is an interceptor for the underlying movement d...
                </text>
            </switch>
        </g>
        <path d="M 560 785.37 L 530 779.51" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 560 750 L 698 750 L 710 762 L 710 850 L 560 850 L 560 750 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 698 750 L 698 762 L 710 762" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 800px; margin-left: 561px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                The PersistenceClient checks the FieldChangeBuffer for all requests and invokes all instance specific handlers that are registered.
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="635" y="804" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    The PersistenceClient che...
                </text>
            </switch>
        </g>
        <path d="M 629.62 635 L 620 610" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 570 669.4 L 310 650 L 260 610" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 570 635 L 708 635 L 720 647 L 720 715 L 570 715 L 570 635 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 708 635 L 708 647 L 720 647" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 675px; margin-left: 571px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                Client &amp; server both only register handlers for the MobileParty instances that they currently control.
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="645" y="679" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Client &amp; server both only...
                </text>
            </switch>
        </g>
        <path d="M 581 255 L 518 360" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 530 175 L 668 175 L 680 187 L 680 255 L 530 255 L 530 175 Z" fill="#ffffff" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 668 175 L 668 187 L 680 187" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)">
            <switch>
                <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 215px; margin-left: 531px;">
                        <div style="box-sizing: border-box; font-size: 0; text-align: center; ">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: #000000; line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                The event is executed serverside and has direct access to the authoritative state
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="605" y="219" fill="#000000" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    The event is executed ser...
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://desk.draw.io/support/solutions/articles/16000042487" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Viewer does not support full SVG 1.1
            </text>
        </a>
    </switch>
</svg>