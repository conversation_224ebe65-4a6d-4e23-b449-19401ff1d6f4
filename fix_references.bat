@echo off
echo ========================================
echo LAN Fight Mod 引用修复脚本
echo ========================================
echo.

set GAME_PATH=D:\Steam\steamapps\common\Mount ^& Blade II Bannerlord

echo 检查游戏路径...
if not exist "%GAME_PATH%" (
    echo 错误: 找不到游戏目录
    echo 当前设置的路径: %GAME_PATH%
    echo.
    echo 请修改此脚本中的GAME_PATH变量为您的实际游戏路径
    echo 常见路径:
    echo   - D:\Steam\steamapps\common\Mount ^& Blade II Bannerlord
    echo   - C:\Program Files ^(x86^)\Steam\steamapps\common\Mount ^& Blade II Bannerlord
    echo   - E:\Games\Mount ^& Blade II Bannerlord
    echo.
    pause
    exit /b 1
)

echo 游戏路径验证成功: %GAME_PATH%
echo.

echo 检查关键DLL文件...
set MISSING_FILES=0

REM 检查主要DLL文件
set DLL_LIST=TaleWorlds.Core.dll TaleWorlds.Library.dll TaleWorlds.MountAndBlade.dll 0Harmony.dll

for %%f in (%DLL_LIST%) do (
    set FOUND=0
    if exist "%GAME_PATH%\bin\Win64_Shipping_Client\%%f" set FOUND=1
    if exist "%GAME_PATH%\Modules\Native\bin\Win64_Shipping_Client\%%f" set FOUND=1
    if exist "%GAME_PATH%\Modules\SandBox\bin\Win64_Shipping_Client\%%f" set FOUND=1
    if exist "%GAME_PATH%\Modules\Bannerlord.Harmony\bin\Win64_Shipping_Client\%%f" set FOUND=1
    
    if !FOUND!==0 (
        echo [缺失] %%f
        set /a MISSING_FILES+=1
    ) else (
        echo [找到] %%f
    )
)

if %MISSING_FILES% gtr 0 (
    echo.
    echo 警告: 发现 %MISSING_FILES% 个缺失的DLL文件
    echo 这可能导致编译失败
    echo.
    echo 建议解决方案:
    echo 1. 验证游戏完整性 ^(Steam: 右键游戏 -^> 属性 -^> 本地文件 -^> 验证文件完整性^)
    echo 2. 重新安装游戏
    echo 3. 检查游戏版本是否为最新版本
    echo.
) else (
    echo.
    echo 所有关键DLL文件都已找到！
    echo.
)

echo 检查LiteNetLib包...
if exist "packages\LiteNetLib.1.3.1\lib\net471\LiteNetLib.dll" (
    echo [找到] LiteNetLib.dll
) else (
    echo [缺失] LiteNetLib.dll
    echo.
    echo 请在Visual Studio中执行以下操作:
    echo 1. 右键点击项目 -^> 管理NuGet程序包
    echo 2. 搜索 "LiteNetLib"
    echo 3. 安装版本 1.3.1
    echo.
)

echo.
echo ========================================
echo 引用检查完成
echo ========================================
echo.
echo 如果发现问题，请按照上述建议进行修复
echo 然后重新运行编译脚本
echo.
pause
