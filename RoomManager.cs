using System;
using System.Collections.Generic;
using System.Linq;
using LiteNetLib;
using TaleWorlds.Library;

namespace LAN_Fight
{
    /// <summary>
    /// 房间状态枚举
    /// </summary>
    public enum RoomState
    {
        Waiting,    // 等待玩家加入
        InGame,     // 游戏中
        Paused,     // 暂停
        Closed      // 房间关闭
    }

    /// <summary>
    /// 玩家信息类
    /// </summary>
    public class PlayerInfo
    {
        public string PlayerId { get; set; }
        public string PlayerName { get; set; }
        public NetPeer Peer { get; set; }
        public DateTime JoinTime { get; set; }
        public bool IsHost { get; set; }
        public bool IsReady { get; set; }

        public PlayerInfo(string playerId, string playerName, NetPeer peer, bool isHost = false)
        {
            PlayerId = playerId;
            PlayerName = playerName;
            Peer = peer;
            IsHost = isHost;
            JoinTime = DateTime.Now;
            IsReady = false;
        }

        public override string ToString()
        {
            return $"{PlayerName} ({PlayerId}) - {(IsHost ? "主机" : "客户端")} - {(IsReady ? "准备" : "未准备")}";
        }
    }

    /// <summary>
    /// 房间信息类
    /// </summary>
    public class RoomInfo
    {
        public string RoomId { get; set; }
        public string RoomName { get; set; }
        public string Password { get; set; }
        public int MaxPlayers { get; set; }
        public RoomState State { get; set; }
        public DateTime CreateTime { get; set; }
        public PlayerInfo Host { get; set; }
        public Dictionary<string, PlayerInfo> Players { get; set; }

        public int CurrentPlayerCount => Players?.Count ?? 0;
        public bool HasPassword => !string.IsNullOrEmpty(Password);
        public bool IsFull => CurrentPlayerCount >= MaxPlayers;

        public RoomInfo(string roomName, string password = "", int maxPlayers = NetworkConfiguration.MAX_CONNECTIONS)
        {
            RoomId = Guid.NewGuid().ToString();
            RoomName = roomName;
            Password = password;
            MaxPlayers = maxPlayers;
            State = RoomState.Waiting;
            CreateTime = DateTime.Now;
            Players = new Dictionary<string, PlayerInfo>();
        }

        public bool CanJoin(string password = "")
        {
            if (State != RoomState.Waiting) return false;
            if (IsFull) return false;
            if (HasPassword && Password != password) return false;
            return true;
        }

        public override string ToString()
        {
            return $"{RoomName} ({CurrentPlayerCount}/{MaxPlayers}) - {State} - {(HasPassword ? "有密码" : "无密码")}";
        }
    }

    /// <summary>
    /// 房间管理器
    /// 负责管理房间的创建、加入、离开和状态同步
    /// </summary>
    public class RoomManager
    {
        private RoomInfo _currentRoom;
        private NetworkManager _networkManager;
        private bool _isHost;

        // 事件
        public event Action<RoomInfo> OnRoomCreated;
        public event Action<PlayerInfo> OnPlayerJoined;
        public event Action<PlayerInfo> OnPlayerLeft;
        public event Action<RoomState> OnRoomStateChanged;
        public event Action<string> OnRoomError;

        public RoomInfo CurrentRoom => _currentRoom;
        public bool IsInRoom => _currentRoom != null;
        public bool IsHost => _isHost;

        public RoomManager(NetworkManager networkManager)
        {
            _networkManager = networkManager ?? throw new ArgumentNullException(nameof(networkManager));
            
            // 订阅网络事件
            _networkManager.OnPlayerJoined += HandlePlayerJoined;
            _networkManager.OnPlayerLeft += HandlePlayerLeft;
            _networkManager.OnMessageReceived += HandleMessageReceived;
        }

        /// <summary>
        /// 创建房间
        /// </summary>
        public bool CreateRoom(string roomName, string password = "", int maxPlayers = NetworkConfiguration.MAX_CONNECTIONS)
        {
            try
            {
                if (IsInRoom)
                {
                    OnRoomError?.Invoke("已在房间中，无法创建新房间");
                    return false;
                }

                // 创建房间信息
                _currentRoom = new RoomInfo(roomName, password, maxPlayers);
                _isHost = true;

                // 添加主机玩家
                var hostPlayer = new PlayerInfo(
                    Guid.NewGuid().ToString(),
                    "Host Player", // 这里可以从游戏中获取玩家名称
                    null,
                    true
                );
                
                _currentRoom.Host = hostPlayer;
                _currentRoom.Players[hostPlayer.PlayerId] = hostPlayer;

                // 启动网络服务器
                _networkManager.StartAsHost();

                OnRoomCreated?.Invoke(_currentRoom);
                Debug.Print($"[LAN Fight] 房间创建成功: {_currentRoom}");
                
                return true;
            }
            catch (Exception ex)
            {
                OnRoomError?.Invoke($"创建房间失败: {ex.Message}");
                Debug.Print($"[LAN Fight] 创建房间失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 加入房间
        /// </summary>
        public bool JoinRoom(string serverAddress, string password = "", string playerName = "Player")
        {
            try
            {
                if (IsInRoom)
                {
                    OnRoomError?.Invoke("已在房间中，无法加入新房间");
                    return false;
                }

                _isHost = false;

                // 连接到服务器
                _networkManager.StartAsClient(serverAddress);

                // 发送加入请求
                var joinMessage = new PlayerJoinMessage(playerName, Guid.NewGuid().ToString());
                SendRoomMessage(joinMessage);

                Debug.Print($"[LAN Fight] 尝试加入房间: {serverAddress}");
                return true;
            }
            catch (Exception ex)
            {
                OnRoomError?.Invoke($"加入房间失败: {ex.Message}");
                Debug.Print($"[LAN Fight] 加入房间失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 离开房间
        /// </summary>
        public void LeaveRoom()
        {
            try
            {
                if (!IsInRoom) return;

                if (_isHost)
                {
                    // 主机关闭房间
                    ChangeRoomState(RoomState.Closed);
                    
                    // 通知所有玩家房间关闭
                    foreach (var player in _currentRoom.Players.Values.Where(p => !p.IsHost))
                    {
                        var leaveMessage = new PlayerLeaveMessage(player.PlayerId, "房间已关闭");
                        _networkManager.SendMessageToPeer(player.Peer, SerializeMessage(leaveMessage));
                    }
                }
                else
                {
                    // 客户端离开房间
                    var leaveMessage = new PlayerLeaveMessage("self", "主动离开");
                    SendRoomMessage(leaveMessage);
                }

                // 断开网络连接
                _networkManager.Disconnect();

                // 清理房间信息
                _currentRoom = null;
                _isHost = false;

                Debug.Print("[LAN Fight] 已离开房间");
            }
            catch (Exception ex)
            {
                OnRoomError?.Invoke($"离开房间时出错: {ex.Message}");
                Debug.Print($"[LAN Fight] 离开房间时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 改变房间状态
        /// </summary>
        public void ChangeRoomState(RoomState newState)
        {
            if (!IsInRoom || !_isHost) return;

            var oldState = _currentRoom.State;
            _currentRoom.State = newState;

            OnRoomStateChanged?.Invoke(newState);
            Debug.Print($"[LAN Fight] 房间状态改变: {oldState} -> {newState}");

            // 同步状态给所有客户端
            var roomInfoMessage = new RoomInfoMessage(
                _currentRoom.RoomName,
                _currentRoom.CurrentPlayerCount,
                _currentRoom.MaxPlayers,
                _currentRoom.HasPassword
            );
            
            BroadcastRoomMessage(roomInfoMessage);
        }

        /// <summary>
        /// 发送房间消息
        /// </summary>
        private void SendRoomMessage(NetworkMessage message)
        {
            string serializedMessage = SerializeMessage(message);
            _networkManager.SendMessageToAll(serializedMessage);
        }

        /// <summary>
        /// 广播房间消息
        /// </summary>
        private void BroadcastRoomMessage(NetworkMessage message)
        {
            if (!_isHost || !IsInRoom) return;

            string serializedMessage = SerializeMessage(message);
            foreach (var player in _currentRoom.Players.Values.Where(p => !p.IsHost))
            {
                _networkManager.SendMessageToPeer(player.Peer, serializedMessage);
            }
        }

        /// <summary>
        /// 序列化消息
        /// </summary>
        private string SerializeMessage(NetworkMessage message)
        {
            // 简单的JSON序列化，实际项目中可以使用更高效的序列化方式
            return $"{message.MessageType}|{message.Timestamp.ToBinary()}|{message.GetType().Name}";
        }

        /// <summary>
        /// 处理玩家加入
        /// </summary>
        private void HandlePlayerJoined(NetPeer peer, string data)
        {
            try
            {
                if (_isHost && IsInRoom)
                {
                    // 解析加入消息
                    // 这里简化处理，实际应该解析完整的消息
                    var playerInfo = new PlayerInfo(
                        Guid.NewGuid().ToString(),
                        $"Player_{peer.EndPoint.Port}",
                        peer
                    );

                    _currentRoom.Players[playerInfo.PlayerId] = playerInfo;
                    OnPlayerJoined?.Invoke(playerInfo);

                    // 发送房间信息给新玩家
                    var roomInfoMessage = new RoomInfoMessage(
                        _currentRoom.RoomName,
                        _currentRoom.CurrentPlayerCount,
                        _currentRoom.MaxPlayers,
                        _currentRoom.HasPassword
                    );
                    
                    _networkManager.SendMessageToPeer(peer, SerializeMessage(roomInfoMessage));
                }
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 处理玩家加入时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理玩家离开
        /// </summary>
        private void HandlePlayerLeft(NetPeer peer, string reason)
        {
            try
            {
                if (IsInRoom)
                {
                    var player = _currentRoom.Players.Values.FirstOrDefault(p => p.Peer == peer);
                    if (player != null)
                    {
                        _currentRoom.Players.Remove(player.PlayerId);
                        OnPlayerLeft?.Invoke(player);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 处理玩家离开时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理接收到的消息
        /// </summary>
        private void HandleMessageReceived(NetPeer peer, string message)
        {
            try
            {
                // 这里可以解析和处理不同类型的房间消息
                Debug.Print($"[LAN Fight] 收到房间消息: {message}");
            }
            catch (Exception ex)
            {
                Debug.Print($"[LAN Fight] 处理房间消息时出错: {ex.Message}");
            }
        }
    }
}
